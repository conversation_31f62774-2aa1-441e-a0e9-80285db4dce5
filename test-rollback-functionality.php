<?php
/**
 * Comprehensive test for rollback functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔄 Rollback Functionality Test</h2>\n";

// Step 1: Verify classes exist
echo "<h3>Step 1: Verify Required Classes</h3>\n";

$classes_exist = true;

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    echo "❌ Redco_Diagnostic_AutoFix_Engine class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix_Engine class found\n";
}

if (!class_exists('Redco_Diagnostic_AutoFix')) {
    echo "❌ Redco_Diagnostic_AutoFix class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix class found\n";
}

if (!$classes_exist) {
    echo "<p style='color: red;'>❌ Required classes not found. Cannot continue test.</p>\n";
    return;
}

// Step 2: Test backup creation and validation
echo "<h3>Step 2: Test Backup Creation and Validation</h3>\n";

$engine = new Redco_Diagnostic_AutoFix_Engine();
$diagnostic = new Redco_Diagnostic_AutoFix();

// Create a test issue that will create a backup
$test_issue = array(
    'id' => 'rollback_test_' . time(),
    'title' => 'Rollback Test Issue',
    'description' => 'Test issue for rollback functionality',
    'severity' => 'low',
    'category' => 'test',
    'auto_fixable' => true,
    'fix_action' => 'test_fix'
);

echo "Creating test fix with backup...\n";

// Apply the test fix (this should create a backup)
$fix_result = $engine->apply_fix($test_issue);

if (isset($fix_result['success']) && $fix_result['success']) {
    echo "✅ Test fix applied successfully\n";
    
    // Check if backup was created
    if (isset($fix_result['backup_file']) && !empty($fix_result['backup_file'])) {
        echo "✅ Backup file created: " . basename($fix_result['backup_file']) . "\n";
        $backup_file = $fix_result['backup_file'];
    } else {
        echo "❌ No backup file information in fix result\n";
        $backup_file = null;
    }
} else {
    echo "❌ Test fix failed: " . ($fix_result['message'] ?? 'Unknown error') . "\n";
    $backup_file = null;
}

// Step 3: Check fix history for rollback ID
echo "<h3>Step 3: Check Fix History for Rollback ID</h3>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions: " . count($fix_history) . "\n";

$rollback_id = null;
if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    echo "Latest session details:\n";
    echo "- Timestamp: " . date('Y-m-d H:i:s', $latest_session['timestamp']) . "\n";
    echo "- Fixes applied: " . $latest_session['fixes_applied'] . "\n";
    echo "- Backup created: " . ($latest_session['backup_created'] ? 'Yes' : 'No') . "\n";
    
    if (isset($latest_session['rollback_id']) && !empty($latest_session['rollback_id'])) {
        $rollback_id = $latest_session['rollback_id'];
        echo "✅ Rollback ID found: " . $rollback_id . "\n";
    } else {
        echo "❌ No rollback ID in latest session\n";
    }
} else {
    echo "❌ No fix history found\n";
}

// Step 4: Test backup validation
echo "<h3>Step 4: Test Backup Validation</h3>\n";

if ($rollback_id) {
    $validation_result = $engine->validate_backup_for_rollback($rollback_id);
    
    if ($validation_result['valid']) {
        echo "✅ Backup validation passed\n";
        echo "Backup path: " . $validation_result['backup_path'] . "\n";
        echo "Validation reason: " . $validation_result['reason'] . "\n";
    } else {
        echo "❌ Backup validation failed: " . $validation_result['reason'] . "\n";
    }
} else {
    echo "❌ Cannot test validation - no rollback ID available\n";
}

// Step 5: Test rollback button visibility
echo "<h3>Step 5: Test Rollback Button Visibility</h3>\n";

// Simulate AJAX call to load recent fixes
$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        $response_data = json_decode($ajax_output, true);
        if ($response_data && isset($response_data['success']) && $response_data['success']) {
            $html = $response_data['data']['html'];
            
            // Check if rollback button is present
            $rollback_button_count = substr_count($html, 'rollback-fix');
            echo "Rollback buttons found in HTML: {$rollback_button_count}\n";
            
            if ($rollback_button_count > 0) {
                echo "✅ Rollback button is visible in recent fixes\n";
                
                // Extract rollback ID from button
                if (preg_match('/data-backup-id="([^"]+)"/', $html, $matches)) {
                    $button_backup_id = $matches[1];
                    echo "Button backup ID: " . $button_backup_id . "\n";
                    
                    if ($button_backup_id === $rollback_id) {
                        echo "✅ Button backup ID matches session rollback ID\n";
                    } else {
                        echo "❌ Button backup ID does not match session rollback ID\n";
                    }
                }
            } else {
                echo "❌ No rollback button found in recent fixes HTML\n";
                echo "HTML preview: " . substr(strip_tags($html), 0, 200) . "...\n";
            }
        } else {
            echo "❌ AJAX response was not successful\n";
        }
    } else {
        echo "❌ AJAX call returned no data\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX call threw exception: " . $e->getMessage() . "\n";
}

// Step 6: Test actual rollback execution
echo "<h3>Step 6: Test Actual Rollback Execution</h3>\n";

if ($rollback_id && $validation_result['valid']) {
    echo "Executing rollback for backup ID: {$rollback_id}\n";
    
    try {
        $rollback_result = $engine->rollback_fixes($rollback_id);
        
        if ($rollback_result['success']) {
            echo "✅ Rollback executed successfully\n";
            echo "Files restored: " . ($rollback_result['files_restored'] ?? 0) . "\n";
            echo "Options restored: " . ($rollback_result['options_restored'] ?? 0) . "\n";
            
            if (isset($rollback_result['fixed_issues_cleared'])) {
                echo "✅ Fixed issues list cleared\n";
            }
            
            if (isset($rollback_result['scan_caches_cleared'])) {
                echo "✅ Scan caches cleared\n";
            }
            
            if (isset($rollback_result['diagnostic_state_reset'])) {
                echo "✅ Diagnostic state reset\n";
            }
            
        } else {
            echo "❌ Rollback failed\n";
            if (isset($rollback_result['errors']) && !empty($rollback_result['errors'])) {
                foreach ($rollback_result['errors'] as $error) {
                    echo "Error: " . $error . "\n";
                }
            }
        }
    } catch (Exception $e) {
        echo "❌ Rollback threw exception: " . $e->getMessage() . "\n";
    }
} else {
    echo "❌ Cannot test rollback execution - validation failed or no rollback ID\n";
}

// Step 7: Verify rollback effects
echo "<h3>Step 7: Verify Rollback Effects</h3>\n";

if (isset($rollback_result) && $rollback_result['success']) {
    // Check if fix history was updated
    $updated_fix_history = get_option('redco_diagnostic_fix_history', array());
    $updated_count = count($updated_fix_history);
    $original_count = count($fix_history);
    
    echo "Fix history count before rollback: {$original_count}\n";
    echo "Fix history count after rollback: {$updated_count}\n";
    
    if ($updated_count < $original_count) {
        echo "✅ Fix history was updated (session removed)\n";
    } else {
        echo "⚠️ Fix history count unchanged\n";
    }
    
    // Check if fixed issues were cleared
    $fixed_issues = get_option('redco_fixed_issues', array());
    echo "Fixed issues count after rollback: " . count($fixed_issues) . "\n";
    
    // Check if diagnostic caches were cleared
    $diagnostic_results = get_option('redco_diagnostic_results');
    if ($diagnostic_results === false) {
        echo "✅ Diagnostic results cache cleared\n";
    } else {
        echo "⚠️ Diagnostic results cache still exists\n";
    }
}

// Step 8: Test rollback history
echo "<h3>Step 8: Test Rollback History</h3>\n";

$rollback_history = get_option('redco_rollback_history', array());
echo "Rollback history entries: " . count($rollback_history) . "\n";

if (!empty($rollback_history)) {
    $latest_rollback = end($rollback_history);
    echo "Latest rollback:\n";
    echo "- Timestamp: " . date('Y-m-d H:i:s', $latest_rollback['timestamp']) . "\n";
    echo "- Action: " . $latest_rollback['action'] . "\n";
    echo "- Backup ID: " . $latest_rollback['backup_id'] . "\n";
    echo "- Message: " . $latest_rollback['message'] . "\n";
    
    if ($latest_rollback['backup_id'] === $rollback_id) {
        echo "✅ Rollback history correctly recorded\n";
    } else {
        echo "❌ Rollback history backup ID mismatch\n";
    }
} else {
    echo "❌ No rollback history found\n";
}

// Step 9: Summary
echo "<h3>Step 9: Test Summary</h3>\n";

$tests_passed = 0;
$total_tests = 8;

// Test 1: Classes exist
if ($classes_exist) {
    echo "✅ Test 1: Required classes available\n";
    $tests_passed++;
} else {
    echo "❌ Test 1: Required classes missing\n";
}

// Test 2: Fix with backup creation
if (isset($fix_result['success']) && $fix_result['success']) {
    echo "✅ Test 2: Fix with backup creation successful\n";
    $tests_passed++;
} else {
    echo "❌ Test 2: Fix with backup creation failed\n";
}

// Test 3: Rollback ID in history
if ($rollback_id) {
    echo "✅ Test 3: Rollback ID found in fix history\n";
    $tests_passed++;
} else {
    echo "❌ Test 3: Rollback ID missing from fix history\n";
}

// Test 4: Backup validation
if (isset($validation_result) && $validation_result['valid']) {
    echo "✅ Test 4: Backup validation successful\n";
    $tests_passed++;
} else {
    echo "❌ Test 4: Backup validation failed\n";
}

// Test 5: Rollback button visibility
if (isset($rollback_button_count) && $rollback_button_count > 0) {
    echo "✅ Test 5: Rollback button visible\n";
    $tests_passed++;
} else {
    echo "❌ Test 5: Rollback button not visible\n";
}

// Test 6: Rollback execution
if (isset($rollback_result) && $rollback_result['success']) {
    echo "✅ Test 6: Rollback execution successful\n";
    $tests_passed++;
} else {
    echo "❌ Test 6: Rollback execution failed\n";
}

// Test 7: Rollback effects verification
if (isset($rollback_result) && $rollback_result['success'] && isset($rollback_result['diagnostic_state_reset'])) {
    echo "✅ Test 7: Rollback effects verified\n";
    $tests_passed++;
} else {
    echo "❌ Test 7: Rollback effects not verified\n";
}

// Test 8: Rollback history
if (!empty($rollback_history) && isset($latest_rollback) && $latest_rollback['backup_id'] === $rollback_id) {
    echo "✅ Test 8: Rollback history recorded\n";
    $tests_passed++;
} else {
    echo "❌ Test 8: Rollback history not recorded properly\n";
}

if ($tests_passed === $total_tests) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED ({$tests_passed}/{$total_tests})</p>\n";
    echo "<h4>✅ Rollback System Fully Functional:</h4>\n";
    echo "<ul>\n";
    echo "<li>✅ Backup creation and validation working</li>\n";
    echo "<li>✅ Rollback button visibility consistent</li>\n";
    echo "<li>✅ Actual file restoration working</li>\n";
    echo "<li>✅ Cache clearing and state reset working</li>\n";
    echo "<li>✅ Fix history management working</li>\n";
    echo "<li>✅ Rollback history tracking working</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ SOME TESTS FAILED ({$tests_passed}/{$total_tests})</p>\n";
    echo "<p>Please check the individual test results above for details.</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> The rollback system now performs actual file restoration and state reset, not simulation.</p>\n";
?>
