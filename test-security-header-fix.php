<?php
/**
 * Test script to verify the security header fix bug is resolved
 * 
 * This script simulates the bug scenario where:
 * 1. Security header fix is applied
 * 2. Fix is verified and marked as successful
 * 3. Issue should be removed from "Recent Issues Found" list
 * 4. Fix should appear in "Recent Fixes" list
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔧 Testing Security Header Fix Bug Resolution</h2>\n";

// Step 1: Simulate the issue detection
echo "<h3>Step 1: Simulating Issue Detection</h3>\n";

$test_issue = array(
    'id' => 'missing_security_header_Strict-Transport-Security',
    'title' => 'Missing Security Header: Strict-Transport-Security',
    'description' => 'Forces HTTPS connections',
    'severity' => 'medium',
    'category' => 'security',
    'auto_fixable' => true,
    'impact' => 'Improves security and SEO',
    'recommendation' => 'Add HSTS header for HTTPS sites',
    'fix_action' => 'add_security_header'
);

echo "✅ Test issue created: " . $test_issue['title'] . "\n";

// Step 2: Apply the fix
echo "<h3>Step 2: Applying Security Header Fix</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    $fix_result = $engine->apply_fix($test_issue);
    
    echo "Fix Result: " . ($fix_result['success'] ? '✅ SUCCESS' : '❌ FAILED') . "\n";
    echo "Message: " . $fix_result['message'] . "\n";
    
    if (isset($fix_result['verification'])) {
        echo "Verification: " . ($fix_result['verification']['verified'] ? '✅ PASSED' : '❌ FAILED') . "\n";
    }
    
    if (isset($fix_result['persistence'])) {
        echo "Persistence: " . ($fix_result['persistence']['persisted'] ? '✅ PASSED' : '❌ FAILED') . "\n";
    }
} else {
    echo "❌ Redco_Diagnostic_AutoFix_Engine class not found\n";
}

// Step 3: Check if issue is properly tracked
echo "<h3>Step 3: Checking Fix Tracking</h3>\n";

$fixed_issues = get_option('redco_fixed_issues', array());
$issue_id = $test_issue['id'];

if (isset($fixed_issues[$issue_id])) {
    echo "✅ Issue properly tracked in fixed_issues list\n";
    echo "Timestamp: " . date('Y-m-d H:i:s', $fixed_issues[$issue_id]['timestamp']) . "\n";
} else {
    echo "❌ Issue NOT tracked in fixed_issues list - THIS IS THE BUG!\n";
}

// Step 4: Run a new scan to test filtering
echo "<h3>Step 4: Testing Issue Filtering in New Scan</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate security issues scan
    $security_headers = array(
        'Strict-Transport-Security' => array(
            'present' => true, // Should be true after fix
            'affects_performance' => true,
            'description' => 'Forces HTTPS connections',
            'performance_impact' => 'Improves security and SEO',
            'recommendation' => 'Add HSTS header for HTTPS sites'
        )
    );
    
    // Check if the issue would still be detected
    if (!$security_headers['Strict-Transport-Security']['present']) {
        echo "❌ Issue would still be detected - fix verification failed\n";
    } else {
        echo "✅ Issue would NOT be detected - fix is working\n";
    }
    
    // Test the filtering logic
    $test_issues = array($test_issue);
    $filtered_issues = array();
    
    foreach ($test_issues as $issue) {
        $issue_id = $issue['id'];
        
        if (isset($fixed_issues[$issue_id])) {
            // This should exclude the issue from results
            echo "✅ Issue filtered out - will not appear in 'Recent Issues Found'\n";
        } else {
            $filtered_issues[] = $issue;
            echo "❌ Issue NOT filtered - would still appear in 'Recent Issues Found'\n";
        }
    }
    
} else {
    echo "❌ Redco_Diagnostic_AutoFix class not found\n";
}

// Step 5: Summary
echo "<h3>Step 5: Test Summary</h3>\n";

$all_tests_passed = true;

// Check if fix was applied
if (!isset($fix_result) || !$fix_result['success']) {
    echo "❌ Fix application failed\n";
    $all_tests_passed = false;
}

// Check if issue was tracked
if (!isset($fixed_issues[$issue_id])) {
    echo "❌ Issue tracking failed\n";
    $all_tests_passed = false;
}

// Check if issue would be filtered
if (isset($filtered_issues) && count($filtered_issues) > 0) {
    echo "❌ Issue filtering failed\n";
    $all_tests_passed = false;
}

if ($all_tests_passed) {
    echo "🎉 ALL TESTS PASSED - Bug is fixed!\n";
    echo "✅ Security header fix is applied correctly\n";
    echo "✅ Fixed issues are properly tracked\n";
    echo "✅ Fixed issues are filtered from new scans\n";
    echo "✅ Issue will not appear in 'Recent Issues Found' after fix\n";
} else {
    echo "⚠️ SOME TESTS FAILED - Bug may still exist\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> This test simulates the bug scenario. In the actual interface, you should see:</p>\n";
echo "<ul>\n";
echo "<li>✅ Fixed issues disappear from 'Recent Issues Found' list</li>\n";
echo "<li>✅ Fixed issues appear in 'Recent Fixes' list</li>\n";
echo "<li>✅ No duplicate entries in both lists</li>\n";
echo "</ul>\n";
?>
