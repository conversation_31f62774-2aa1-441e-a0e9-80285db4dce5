<?php
/**
 * Debug script to check fix history structure
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔍 Debug Fix History Structure</h2>\n";

// Get current fix history
$fix_history = get_option('redco_diagnostic_fix_history', array());

echo "<h3>Fix History Overview</h3>\n";
echo "Total sessions: " . count($fix_history) . "\n";
echo "Option exists: " . (get_option('redco_diagnostic_fix_history') !== false ? 'Yes' : 'No') . "\n";

if (empty($fix_history)) {
    echo "<p style='color: red;'>❌ Fix history is empty!</p>\n";
    
    // Check if the option exists but is empty
    $option_value = get_option('redco_diagnostic_fix_history', 'NOT_FOUND');
    if ($option_value === 'NOT_FOUND') {
        echo "<p>Option does not exist in database</p>\n";
    } else {
        echo "<p>Option exists but is empty: " . var_export($option_value, true) . "</p>\n";
    }
} else {
    echo "<h3>Recent Sessions (Last 5)</h3>\n";
    $recent_sessions = array_slice(array_reverse($fix_history), 0, 5);
    
    foreach ($recent_sessions as $index => $session) {
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>Session " . ($index + 1) . "</h4>\n";
        echo "<ul>\n";
        echo "<li><strong>Timestamp:</strong> " . (isset($session['timestamp']) ? date('Y-m-d H:i:s', $session['timestamp']) : 'Missing') . "</li>\n";
        echo "<li><strong>Fixes Applied:</strong> " . (isset($session['fixes_applied']) ? $session['fixes_applied'] : 'Missing') . "</li>\n";
        echo "<li><strong>Fixes Failed:</strong> " . (isset($session['fixes_failed']) ? $session['fixes_failed'] : 'Missing') . "</li>\n";
        echo "<li><strong>Backup Created:</strong> " . (isset($session['backup_created']) ? ($session['backup_created'] ? 'Yes' : 'No') : 'Missing') . "</li>\n";
        echo "<li><strong>Rollback ID:</strong> " . (isset($session['rollback_id']) ? $session['rollback_id'] : 'Missing') . "</li>\n";
        echo "</ul>\n";
        
        if (isset($session['details']) && is_array($session['details'])) {
            echo "<h5>Fix Details (" . count($session['details']) . " items)</h5>\n";
            foreach ($session['details'] as $detail_index => $detail) {
                echo "<div style='margin-left: 20px; border-left: 2px solid #ddd; padding-left: 10px;'>\n";
                echo "<strong>Fix " . ($detail_index + 1) . ":</strong><br>\n";
                echo "Issue ID: " . (isset($detail['issue_id']) ? $detail['issue_id'] : 'Missing') . "<br>\n";
                echo "Issue Title: " . (isset($detail['issue_title']) ? $detail['issue_title'] : 'Missing') . "<br>\n";
                echo "Success: " . (isset($detail['success']) ? ($detail['success'] ? 'Yes' : 'No') : 'Missing') . "<br>\n";
                echo "Message: " . (isset($detail['message']) ? $detail['message'] : 'Missing') . "<br>\n";
                echo "</div>\n";
            }
        } else {
            echo "<p style='color: orange;'>⚠️ No fix details found</p>\n";
        }
        
        echo "</div>\n";
    }
}

// Test creating a fix session manually
echo "<h3>Test: Create Manual Fix Session</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    
    // Create a simple test issue
    $test_issue = array(
        'id' => 'debug_test_' . time(),
        'title' => 'Debug Test Issue',
        'description' => 'Test issue for debugging fix history',
        'severity' => 'low',
        'category' => 'test',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    );
    
    echo "Creating test fix session...\n";
    
    try {
        $results = $engine->apply_auto_fixes(array($test_issue), false);
        
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>Test Results:</h4>\n";
        echo "<pre>" . print_r($results, true) . "</pre>\n";
        echo "</div>\n";
        
        // Check if fix history was updated
        $updated_history = get_option('redco_diagnostic_fix_history', array());
        $new_count = count($updated_history);
        $old_count = count($fix_history);
        
        if ($new_count > $old_count) {
            echo "<p style='color: green;'>✅ Fix history was updated! New count: {$new_count}</p>\n";
            
            // Show the latest session
            $latest_session = end($updated_history);
            echo "<h4>Latest Session:</h4>\n";
            echo "<pre>" . print_r($latest_session, true) . "</pre>\n";
        } else {
            echo "<p style='color: red;'>❌ Fix history was NOT updated. Count remains: {$old_count}</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error creating test session: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Redco_Diagnostic_AutoFix_Engine class not found</p>\n";
}

// Check database directly
echo "<h3>Database Check</h3>\n";
global $wpdb;

$option_row = $wpdb->get_row($wpdb->prepare(
    "SELECT option_value FROM {$wpdb->options} WHERE option_name = %s",
    'redco_diagnostic_fix_history'
));

if ($option_row) {
    echo "<p>✅ Option exists in database</p>\n";
    $db_value = maybe_unserialize($option_row->option_value);
    echo "<p>Database value type: " . gettype($db_value) . "</p>\n";
    echo "<p>Database value count: " . (is_array($db_value) ? count($db_value) : 'Not array') . "</p>\n";
} else {
    echo "<p style='color: red;'>❌ Option does not exist in database</p>\n";
}

// Summary
echo "<h3>Summary</h3>\n";
if (empty($fix_history)) {
    echo "<p style='color: red;'>🚨 ISSUE: Fix history is empty</p>\n";
    echo "<p>Possible causes:</p>\n";
    echo "<ul>\n";
    echo "<li>Fixes are not being applied through the engine</li>\n";
    echo "<li>record_fix_session() method is not being called</li>\n";
    echo "<li>Database update is failing</li>\n";
    echo "<li>Option is being cleared by another process</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: green;'>✅ Fix history contains data</p>\n";
    echo "<p>Recent fixes should be visible in the interface</p>\n";
}
?>
