<?php
/**
 * Comprehensive test for recent fixes functionality
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔧 Comprehensive Recent Fixes Test</h2>\n";

// Step 1: Clear existing history for clean test
echo "<h3>Step 1: Clean Test Environment</h3>\n";

delete_option('redco_diagnostic_fix_history');
delete_option('redco_fixed_issues');

echo "✅ Cleared existing fix history and fixed issues\n";

// Step 2: Verify classes exist
echo "<h3>Step 2: Verify Classes</h3>\n";

$classes_exist = true;

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    echo "❌ Redco_Diagnostic_AutoFix_Engine class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix_Engine class found\n";
}

if (!class_exists('Redco_Diagnostic_AutoFix')) {
    echo "❌ Redco_Diagnostic_AutoFix class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix class found\n";
}

if (!$classes_exist) {
    echo "<p style='color: red;'>❌ Required classes not found. Cannot continue test.</p>\n";
    return;
}

// Step 3: Apply multiple test fixes
echo "<h3>Step 3: Apply Multiple Test Fixes</h3>\n";

$engine = new Redco_Diagnostic_AutoFix_Engine();
$diagnostic = new Redco_Diagnostic_AutoFix();

$test_issues = array(
    array(
        'id' => 'test_fix_1_' . time(),
        'title' => 'Test Fix #1 - Security Header',
        'description' => 'Test security header fix',
        'severity' => 'medium',
        'category' => 'security',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    ),
    array(
        'id' => 'test_fix_2_' . time(),
        'title' => 'Test Fix #2 - Performance',
        'description' => 'Test performance optimization',
        'severity' => 'high',
        'category' => 'performance',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    ),
    array(
        'id' => 'test_fix_3_' . time(),
        'title' => 'Test Fix #3 - Caching',
        'description' => 'Test caching improvement',
        'severity' => 'low',
        'category' => 'caching',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    )
);

$successful_fixes = 0;

foreach ($test_issues as $index => $issue) {
    echo "Applying fix " . ($index + 1) . ": " . $issue['title'] . "\n";
    
    try {
        // Use the new apply_fix method
        $fix_result = $engine->apply_fix($issue);
        
        if (isset($fix_result['success']) && $fix_result['success']) {
            echo "✅ Fix " . ($index + 1) . " applied successfully\n";
            $successful_fixes++;
            
            // Also track in the main diagnostic class
            $diagnostic->track_fixed_issue($issue['id'], $fix_result);
        } else {
            echo "❌ Fix " . ($index + 1) . " failed: " . ($fix_result['message'] ?? 'Unknown error') . "\n";
        }
    } catch (Exception $e) {
        echo "❌ Fix " . ($index + 1) . " threw exception: " . $e->getMessage() . "\n";
    }
    
    // Small delay between fixes
    usleep(100000); // 0.1 seconds
}

echo "Total successful fixes: {$successful_fixes}\n";

// Step 4: Check fix history
echo "<h3>Step 4: Check Fix History</h3>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history sessions: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    echo "<h4>Recent Sessions:</h4>\n";
    foreach (array_slice($fix_history, -3) as $index => $session) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>\n";
        echo "<strong>Session " . ($index + 1) . ":</strong><br>\n";
        echo "Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "<br>\n";
        echo "Fixes Applied: " . $session['fixes_applied'] . "<br>\n";
        echo "Fixes Failed: " . $session['fixes_failed'] . "<br>\n";
        
        if (isset($session['details']) && is_array($session['details'])) {
            echo "Fix Details: " . count($session['details']) . " items<br>\n";
            foreach ($session['details'] as $detail) {
                echo "- " . ($detail['issue_title'] ?? 'Unknown') . " (" . (($detail['success'] ?? false) ? 'Success' : 'Failed') . ")<br>\n";
            }
        }
        echo "</div>\n";
    }
} else {
    echo "<p style='color: red;'>❌ No fix history found</p>\n";
}

// Step 5: Check fixed issues tracking
echo "<h3>Step 5: Check Fixed Issues Tracking</h3>\n";

$fixed_issues = get_option('redco_fixed_issues', array());
echo "Fixed issues count: " . count($fixed_issues) . "\n";

if (!empty($fixed_issues)) {
    echo "<h4>Fixed Issues:</h4>\n";
    foreach ($fixed_issues as $issue_id => $fix_info) {
        echo "- {$issue_id}: Fixed at " . date('Y-m-d H:i:s', $fix_info['timestamp']) . "\n";
    }
} else {
    echo "<p style='color: red;'>❌ No fixed issues tracked</p>\n";
}

// Step 6: Test AJAX endpoint
echo "<h3>Step 6: Test AJAX Endpoint</h3>\n";

$_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');

ob_start();
try {
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        echo "✅ AJAX endpoint returned data\n";
        
        // Try to decode the JSON response
        $response_data = json_decode($ajax_output, true);
        if ($response_data && isset($response_data['success'])) {
            echo "Response success: " . ($response_data['success'] ? 'true' : 'false') . "\n";
            if ($response_data['success'] && isset($response_data['data']['html'])) {
                $html_length = strlen($response_data['data']['html']);
                echo "HTML content length: {$html_length} characters\n";
                
                // Check if HTML contains fix entries
                $fix_count = substr_count($response_data['data']['html'], 'recent-fix-item');
                echo "Fix items in HTML: {$fix_count}\n";
            }
        } else {
            echo "Raw AJAX output:\n";
            echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>\n";
        }
    } else {
        echo "❌ AJAX endpoint returned no data\n";
    }
} catch (Exception $e) {
    ob_end_clean();
    echo "❌ AJAX endpoint threw exception: " . $e->getMessage() . "\n";
}

// Step 7: Summary and recommendations
echo "<h3>Step 7: Test Summary</h3>\n";

$issues_found = array();

if ($successful_fixes === 0) {
    $issues_found[] = "No fixes were applied successfully";
}

if (empty($fix_history)) {
    $issues_found[] = "Fix history is empty";
}

if (empty($fixed_issues)) {
    $issues_found[] = "Fixed issues tracking is empty";
}

if (empty($ajax_output)) {
    $issues_found[] = "AJAX endpoint returned no data";
}

if (empty($issues_found)) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED!</p>\n";
    echo "<ul>\n";
    echo "<li>✅ {$successful_fixes} fixes applied successfully</li>\n";
    echo "<li>✅ Fix history contains " . count($fix_history) . " sessions</li>\n";
    echo "<li>✅ Fixed issues tracking contains " . count($fixed_issues) . " items</li>\n";
    echo "<li>✅ AJAX endpoint is working</li>\n";
    echo "<li>✅ Recent fixes should be visible in the interface</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🎯 Expected Results in Interface:</h4>\n";
    echo "<ul>\n";
    echo "<li>Recent Fixes list should show up to 5 recent fixes</li>\n";
    echo "<li>Each fix should display title, timestamp, and status</li>\n";
    echo "<li>Fixed issues should not appear in 'Recent Issues Found'</li>\n";
    echo "<li>Cache should be cleared after each new fix</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ ISSUES FOUND:</p>\n";
    echo "<ul>\n";
    foreach ($issues_found as $issue) {
        echo "<li style='color: red;'>❌ " . $issue . "</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h4>🔧 Debugging Steps:</h4>\n";
    echo "<ol>\n";
    echo "<li>Check PHP error logs for any errors during fix application</li>\n";
    echo "<li>Verify WordPress database permissions</li>\n";
    echo "<li>Check if any caching plugins are interfering</li>\n";
    echo "<li>Verify AJAX nonce is working correctly</li>\n";
    echo "<li>Check browser console for JavaScript errors</li>\n";
    echo "</ol>\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> This test creates test fixes to verify the system is working. In the actual interface, you should see real fixes from diagnostic scans.</p>\n";
?>
