<?php
/**
 * Test script to verify the recent fixes list is working properly
 * 
 * This script tests:
 * 1. Fix history recording
 * 2. Recent fixes display
 * 3. Issue tracking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔧 Testing Recent Fixes List</h2>\n";

// Step 1: Check current fix history
echo "<h3>Step 1: Current Fix History</h3>\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    echo "<ul>\n";
    foreach (array_slice($fix_history, -5) as $index => $session) {
        $timestamp = isset($session['timestamp']) ? date('Y-m-d H:i:s', $session['timestamp']) : 'Unknown';
        $fixes_applied = isset($session['fixes_applied']) ? $session['fixes_applied'] : 0;
        echo "<li>Session " . ($index + 1) . ": {$fixes_applied} fixes applied at {$timestamp}</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "❌ No fix history found\n";
}

// Step 2: Check fixed issues tracking
echo "<h3>Step 2: Fixed Issues Tracking</h3>\n";

$fixed_issues = get_option('redco_fixed_issues', array());
echo "Fixed issues count: " . count($fixed_issues) . "\n";

if (!empty($fixed_issues)) {
    echo "<ul>\n";
    foreach (array_slice($fixed_issues, -5, 5, true) as $issue_id => $fix_info) {
        $timestamp = isset($fix_info['timestamp']) ? date('Y-m-d H:i:s', $fix_info['timestamp']) : 'Unknown';
        echo "<li>Issue ID: {$issue_id} - Fixed at {$timestamp}</li>\n";
    }
    echo "</ul>\n";
} else {
    echo "❌ No fixed issues found\n";
}

// Step 3: Test the AJAX endpoint for recent fixes
echo "<h3>Step 3: Testing Recent Fixes AJAX Endpoint</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate the AJAX call
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    
    // Capture the output
    ob_start();
    $diagnostic->ajax_load_recent_fixes();
    $ajax_output = ob_get_clean();
    
    if (!empty($ajax_output)) {
        echo "✅ AJAX endpoint returned data\n";
        echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>\n";
    } else {
        echo "❌ AJAX endpoint returned no data\n";
    }
} else {
    echo "❌ Redco_Diagnostic_AutoFix class not found\n";
}

// Step 4: Create a test fix session
echo "<h3>Step 4: Creating Test Fix Session</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    
    // Create a test issue
    $test_issue = array(
        'id' => 'test_issue_' . time(),
        'title' => 'Test Issue for Recent Fixes',
        'description' => 'This is a test issue to verify recent fixes functionality',
        'severity' => 'medium',
        'category' => 'test',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    );
    
    echo "Creating test fix session...\n";
    
    // Apply the test fix
    $results = $engine->apply_auto_fixes(array($test_issue), false);
    
    if ($results['fixes_applied'] > 0) {
        echo "✅ Test fix applied successfully\n";
        echo "Fixes applied: " . $results['fixes_applied'] . "\n";
        echo "Fixes failed: " . $results['fixes_failed'] . "\n";
    } else {
        echo "❌ Test fix failed\n";
        if (!empty($results['fix_details'])) {
            $fix_detail = $results['fix_details'][0];
            echo "Error: " . ($fix_detail['message'] ?? 'Unknown error') . "\n";
        }
    }
} else {
    echo "❌ Redco_Diagnostic_AutoFix_Engine class not found\n";
}

// Step 5: Check fix history after test
echo "<h3>Step 5: Fix History After Test</h3>\n";

$fix_history_after = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after test: " . count($fix_history_after) . "\n";

if (count($fix_history_after) > count($fix_history)) {
    echo "✅ New fix session was recorded\n";
    $latest_session = end($fix_history_after);
    $timestamp = isset($latest_session['timestamp']) ? date('Y-m-d H:i:s', $latest_session['timestamp']) : 'Unknown';
    $fixes_applied = isset($latest_session['fixes_applied']) ? $latest_session['fixes_applied'] : 0;
    echo "Latest session: {$fixes_applied} fixes applied at {$timestamp}\n";
} else {
    echo "❌ No new fix session was recorded\n";
}

// Step 6: Summary
echo "<h3>Step 6: Test Summary</h3>\n";

$all_tests_passed = true;

// Check if fix history exists
if (empty($fix_history_after)) {
    echo "❌ Fix history is empty\n";
    $all_tests_passed = false;
}

// Check if AJAX endpoint works
if (empty($ajax_output)) {
    echo "❌ AJAX endpoint not working\n";
    $all_tests_passed = false;
}

// Check if test fix was recorded
if (count($fix_history_after) <= count($fix_history)) {
    echo "❌ Test fix was not recorded\n";
    $all_tests_passed = false;
}

if ($all_tests_passed) {
    echo "🎉 ALL TESTS PASSED - Recent fixes functionality is working!\n";
    echo "✅ Fix history is being recorded\n";
    echo "✅ AJAX endpoint is working\n";
    echo "✅ New fixes are being tracked\n";
} else {
    echo "⚠️ SOME TESTS FAILED - Recent fixes may not be working properly\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> If tests failed, check the following:</p>\n";
echo "<ul>\n";
echo "<li>WordPress database permissions</li>\n";
echo "<li>Plugin file permissions</li>\n";
echo "<li>PHP error logs for any issues</li>\n";
echo "<li>Browser console for JavaScript errors</li>\n";
echo "</ul>\n";
?>
