<?php
/**
 * Test script to verify the GZIP compression contradiction has been resolved
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🔧 GZIP Compression Contradiction Fix Test</h2>\n";

// Step 1: Verify classes exist
echo "<h3>Step 1: Verify Required Classes</h3>\n";

$classes_exist = true;

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    echo "❌ Redco_Diagnostic_AutoFix_Engine class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix_Engine class found\n";
}

if (!class_exists('Redco_Diagnostic_AutoFix')) {
    echo "❌ Redco_Diagnostic_AutoFix class not found\n";
    $classes_exist = false;
} else {
    echo "✅ Redco_Diagnostic_AutoFix class found\n";
}

if (!$classes_exist) {
    echo "<p style='color: red;'>❌ Required classes not found. Cannot continue test.</p>\n";
    return;
}

// Step 2: Test comprehensive compression detection
echo "<h3>Step 2: Test Comprehensive Compression Detection</h3>\n";

$engine = new Redco_Diagnostic_AutoFix_Engine();
$diagnostic = new Redco_Diagnostic_AutoFix();

// Test the comprehensive detection method
$engine_detection = $engine->is_compression_enabled_comprehensive();
echo "Engine comprehensive detection: " . ($engine_detection ? '✅ ENABLED' : '❌ DISABLED') . "\n";

// Test individual detection methods using reflection
$reflection = new ReflectionClass($engine);

$htaccess_method = $reflection->getMethod('check_htaccess_compression_rules');
$htaccess_method->setAccessible(true);
$htaccess_detection = $htaccess_method->invoke($engine);

$http_method = $reflection->getMethod('check_http_compression_headers');
$http_method->setAccessible(true);
$http_detection = $http_method->invoke($engine);

$server_method = $reflection->getMethod('check_server_compression_modules');
$server_method->setAccessible(true);
$server_detection = $server_method->invoke($engine);

echo "<h4>Detection Method Breakdown:</h4>\n";
echo "- .htaccess rules: " . ($htaccess_detection ? '✅ Found' : '❌ Not found') . "\n";
echo "- HTTP headers: " . ($http_detection ? '✅ Working' : '❌ Not working') . "\n";
echo "- Server modules: " . ($server_detection ? '✅ Available' : '❌ Not available') . "\n";

// Step 3: Test diagnostic scanner detection
echo "<h3>Step 3: Test Diagnostic Scanner Detection</h3>\n";

// Get diagnostic scan results
$scan_results = $diagnostic->run_comprehensive_scan();
$compression_issue_found = false;

if (isset($scan_results['issues'])) {
    foreach ($scan_results['issues'] as $issue) {
        if ($issue['id'] === 'no_compression' || $issue['fix_action'] === 'enable_gzip_compression') {
            $compression_issue_found = true;
            echo "❌ Diagnostic scanner found compression issue: " . $issue['title'] . "\n";
            break;
        }
    }
}

if (!$compression_issue_found) {
    echo "✅ Diagnostic scanner did NOT find compression issues\n";
}

// Step 4: Test auto-fix response
echo "<h3>Step 4: Test Auto-Fix Response</h3>\n";

// Create a test compression issue
$test_issue = array(
    'id' => 'no_compression',
    'title' => 'GZIP Compression Test',
    'fix_action' => 'enable_gzip_compression'
);

$result = array('success' => false, 'changes_made' => array());

// Use reflection to access the fix method
$fix_method = $reflection->getMethod('fix_enable_gzip_compression');
$fix_method->setAccessible(true);

$fix_result = $fix_method->invoke($engine, $test_issue, $result);

echo "Auto-fix result: " . ($fix_result['success'] ? '✅ SUCCESS' : '❌ FAILED') . "\n";
echo "Auto-fix message: " . $fix_result['message'] . "\n";

if (!empty($fix_result['changes_made'])) {
    echo "Changes made:\n";
    foreach ($fix_result['changes_made'] as $change) {
        echo "- " . $change . "\n";
    }
}

// Step 5: Test consistency between scanner and auto-fix
echo "<h3>Step 5: Test Scanner vs Auto-Fix Consistency</h3>\n";

$scanner_says_issue = $compression_issue_found;
$autofix_says_resolved = $fix_result['success'] && strpos($fix_result['message'], 'already exist') !== false;

echo "Scanner says issue exists: " . ($scanner_says_issue ? 'YES' : 'NO') . "\n";
echo "Auto-fix says already resolved: " . ($autofix_says_resolved ? 'YES' : 'NO') . "\n";

// Check for contradiction
$has_contradiction = $scanner_says_issue && $autofix_says_resolved;

if ($has_contradiction) {
    echo "<p style='color: red; font-weight: bold;'>❌ CONTRADICTION DETECTED!</p>\n";
    echo "<p>The scanner reports an issue but auto-fix says it's already resolved.</p>\n";
} else {
    echo "<p style='color: green; font-weight: bold;'>✅ NO CONTRADICTION - Systems are synchronized!</p>\n";
}

// Step 6: Test with existing compression rules
echo "<h3>Step 6: Test with Existing Compression Rules</h3>\n";

$htaccess_file = ABSPATH . '.htaccess';
$backup_file = null;

// Create backup if .htaccess exists
if (file_exists($htaccess_file)) {
    $backup_file = $htaccess_file . '.test-backup-' . time();
    copy($htaccess_file, $backup_file);
    echo "✅ Created backup of .htaccess\n";
}

// Add generic compression rules (not Redco-specific)
$generic_compression = "\n# Generic GZIP Compression\n<IfModule mod_deflate.c>\n    AddOutputFilterByType DEFLATE text/html\n    AddOutputFilterByType DEFLATE text/css\n    AddOutputFilterByType DEFLATE application/javascript\n</IfModule>\n\n";

$current_content = file_exists($htaccess_file) ? file_get_contents($htaccess_file) : '';
$new_content = $generic_compression . $current_content;
file_put_contents($htaccess_file, $new_content);

echo "✅ Added generic compression rules to .htaccess\n";

// Re-test detection
$new_engine_detection = $engine->is_compression_enabled_comprehensive();
$new_htaccess_detection = $htaccess_method->invoke($engine);

echo "Detection after adding generic rules:\n";
echo "- Comprehensive: " . ($new_engine_detection ? '✅ ENABLED' : '❌ DISABLED') . "\n";
echo "- .htaccess: " . ($new_htaccess_detection ? '✅ Found' : '❌ Not found') . "\n";

// Re-test scanner
$new_scan_results = $diagnostic->run_comprehensive_scan();
$new_compression_issue_found = false;

if (isset($new_scan_results['issues'])) {
    foreach ($new_scan_results['issues'] as $issue) {
        if ($issue['id'] === 'no_compression' || $issue['fix_action'] === 'enable_gzip_compression') {
            $new_compression_issue_found = true;
            break;
        }
    }
}

echo "Scanner after adding generic rules: " . ($new_compression_issue_found ? '❌ Still reports issue' : '✅ No issue found') . "\n";

// Re-test auto-fix
$new_fix_result = $fix_method->invoke($engine, $test_issue, $result);
echo "Auto-fix after adding generic rules: " . $new_fix_result['message'] . "\n";

// Step 7: Restore original .htaccess
echo "<h3>Step 7: Cleanup</h3>\n";

if ($backup_file && file_exists($backup_file)) {
    copy($backup_file, $htaccess_file);
    unlink($backup_file);
    echo "✅ Restored original .htaccess\n";
} else {
    // Remove the test compression rules
    $restored_content = str_replace($generic_compression, '', $new_content);
    file_put_contents($htaccess_file, $restored_content);
    echo "✅ Removed test compression rules\n";
}

// Step 8: Final Summary
echo "<h3>Step 8: Test Summary</h3>\n";

$tests_passed = 0;
$total_tests = 4;

// Test 1: Comprehensive detection working
if ($engine_detection !== null) {
    echo "✅ Test 1: Comprehensive detection method working\n";
    $tests_passed++;
} else {
    echo "❌ Test 1: Comprehensive detection method failed\n";
}

// Test 2: No contradiction in current state
if (!$has_contradiction) {
    echo "✅ Test 2: No contradiction between scanner and auto-fix\n";
    $tests_passed++;
} else {
    echo "❌ Test 2: Contradiction still exists\n";
}

// Test 3: Detection works with generic rules
if ($new_engine_detection && !$new_compression_issue_found) {
    echo "✅ Test 3: System correctly detects generic compression rules\n";
    $tests_passed++;
} else {
    echo "❌ Test 3: System failed to detect generic compression rules\n";
}

// Test 4: Auto-fix responds correctly to existing rules
if (strpos($new_fix_result['message'], 'already exist') !== false || strpos($new_fix_result['message'], 'Verified') !== false) {
    echo "✅ Test 4: Auto-fix correctly identifies existing compression\n";
    $tests_passed++;
} else {
    echo "❌ Test 4: Auto-fix failed to identify existing compression\n";
}

if ($tests_passed === $total_tests) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED ({$tests_passed}/{$total_tests})</p>\n";
    echo "<h4>✅ Contradiction Resolution Successful:</h4>\n";
    echo "<ul>\n";
    echo "<li>✅ Unified compression detection across all components</li>\n";
    echo "<li>✅ Scanner and auto-fix use same detection logic</li>\n";
    echo "<li>✅ Generic compression rules are properly detected</li>\n";
    echo "<li>✅ No false positives in diagnostic scans</li>\n";
    echo "<li>✅ Auto-fix correctly identifies existing configurations</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ SOME TESTS FAILED ({$tests_passed}/{$total_tests})</p>\n";
    echo "<p>The contradiction may not be fully resolved. Please check the individual test results.</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> The comprehensive detection system now ensures consistent results between diagnostic scanning and auto-fix operations.</p>\n";
?>
