<?php
// Check fix history for rollback buttons
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

// Load the plugin functions
require_once('includes/helpers.php');

echo "=== BACKUP DIRECTORY INVESTIGATION ===\n";

// Check what redco_diagnostic_get_cache_dir() returns
if (function_exists('redco_diagnostic_get_cache_dir')) {
    $cache_dir = redco_diagnostic_get_cache_dir();
    echo "Cache dir: $cache_dir\n";

    $backup_dir = $cache_dir . 'diagnostic-backups/';
    echo "Expected backup dir: $backup_dir\n";
    echo "Backup dir exists: " . (is_dir($backup_dir) ? 'Yes' : 'No') . "\n";
    echo "Backup dir writable: " . (is_writable($backup_dir) ? 'Yes' : 'No') . "\n";

    // Try to create the directory
    if (!is_dir($backup_dir)) {
        echo "Attempting to create backup directory...\n";
        $created = wp_mkdir_p($backup_dir);
        echo "Directory creation result: " . ($created ? 'Success' : 'Failed') . "\n";

        if (!$created) {
            echo "Parent directory exists: " . (is_dir(dirname($backup_dir)) ? 'Yes' : 'No') . "\n";
            echo "Parent directory writable: " . (is_writable(dirname($backup_dir)) ? 'Yes' : 'No') . "\n";
        }
    }
} else {
    echo "redco_diagnostic_get_cache_dir() function not found!\n";

    // Check if redco_get_cache_dir exists (from main plugin)
    if (function_exists('redco_get_cache_dir')) {
        echo "Found redco_get_cache_dir() from main plugin\n";
        $cache_dir = redco_get_cache_dir();
        echo "Main plugin cache dir: $cache_dir\n";

        $backup_dir = $cache_dir . 'diagnostic-backups/';
        echo "Main plugin backup dir: $backup_dir\n";
        echo "Main plugin backup dir exists: " . (is_dir($backup_dir) ? 'Yes' : 'No') . "\n";
    }
}

echo "\n=== FIX HISTORY ===\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (empty($fix_history)) {
    echo "No fix history found!\n";
    echo "This is why rollback buttons are not showing.\n";
} else {
    foreach ($fix_history as $i => $session) {
        echo "Session " . ($i + 1) . ":\n";
        echo "  Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "\n";
        echo "  Fixes applied: " . $session['fixes_applied'] . "\n";
        echo "  Backup created: " . (isset($session['backup_created']) && $session['backup_created'] ? 'Yes' : 'No') . "\n";
        echo "  Rollback ID: " . (isset($session['rollback_id']) ? $session['rollback_id'] : 'NULL') . "\n";
        echo "\n";
    }
}
