<?php
// Check fix history for rollback buttons in the REAL Redco Optimizer plugin
require_once('d:/xampp/htdocs/wordpress/wp-config.php');

echo "=== REAL REDCO OPTIMIZER FIX HISTORY ===\n";

$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (empty($fix_history)) {
    echo "No fix history found!\n";
    echo "This is why rollback buttons are not showing.\n";
} else {
    foreach ($fix_history as $i => $session) {
        echo "Session " . ($i + 1) . ":\n";
        echo "  Timestamp: " . date('Y-m-d H:i:s', $session['timestamp']) . "\n";
        echo "  Fixes applied: " . $session['fixes_applied'] . "\n";
        echo "  Backup created: " . (isset($session['backup_created']) && $session['backup_created'] ? 'Yes' : 'No') . "\n";
        echo "  Rollback ID: " . (isset($session['rollback_id']) ? $session['rollback_id'] : 'NULL') . "\n";
        echo "\n";
    }
}

// Check backup directory in the real plugin
if (function_exists('redco_get_cache_dir')) {
    $cache_dir = redco_get_cache_dir();
    echo "Real plugin cache dir: $cache_dir\n";
    
    $backup_dir = $cache_dir . 'diagnostic-backups/';
    echo "Real plugin backup dir: $backup_dir\n";
    echo "Backup dir exists: " . (is_dir($backup_dir) ? 'Yes' : 'No') . "\n";
    
    if (is_dir($backup_dir)) {
        $backups = glob($backup_dir . '*');
        echo "Backup files found: " . count($backups) . "\n";
        foreach ($backups as $backup) {
            echo "  " . basename($backup) . "\n";
        }
    }
} else {
    echo "redco_get_cache_dir() function not found in real plugin!\n";
}
