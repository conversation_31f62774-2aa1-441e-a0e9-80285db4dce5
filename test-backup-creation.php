<?php
// Test backup creation
require_once('d:/xampp/htdocs/wordpress/wp-config.php');
require_once('includes/helpers.php');
require_once('modules/diagnostic-autofix/class-diagnostic-autofix-engine.php');

echo "=== TESTING BACKUP CREATION ===\n";

// Create a test issue
$test_issue = array(
    'id' => 'test_fix_backup',
    'title' => 'Test Fix for Backup',
    'fix_action' => 'test_fix',
    'auto_fixable' => true
);

echo "Creating AutoFix Engine...\n";
$engine = new Redco_Diagnostic_AutoFix_Engine();

echo "Applying test fix...\n";
$result = $engine->apply_fix($test_issue);

echo "Fix result:\n";
echo "  Success: " . ($result['success'] ? 'Yes' : 'No') . "\n";
echo "  Message: " . $result['message'] . "\n";

// Check fix history again
echo "\n=== CHECKING FIX HISTORY AFTER TEST ===\n";
$fix_history = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count: " . count($fix_history) . "\n";

if (!empty($fix_history)) {
    $latest_session = end($fix_history);
    echo "Latest session:\n";
    echo "  Timestamp: " . date('Y-m-d H:i:s', $latest_session['timestamp']) . "\n";
    echo "  Fixes applied: " . $latest_session['fixes_applied'] . "\n";
    echo "  Backup created: " . (isset($latest_session['backup_created']) && $latest_session['backup_created'] ? 'Yes' : 'No') . "\n";
    echo "  Rollback ID: " . (isset($latest_session['rollback_id']) ? $latest_session['rollback_id'] : 'NULL') . "\n";
}

// Check if backup directory has any files now
if (function_exists('redco_diagnostic_get_cache_dir')) {
    $cache_dir = redco_diagnostic_get_cache_dir();
    $backup_dir = $cache_dir . 'diagnostic-backups/';
    
    if (is_dir($backup_dir)) {
        $backups = glob($backup_dir . '*');
        echo "\nBackup files found: " . count($backups) . "\n";
        foreach ($backups as $backup) {
            echo "  " . basename($backup) . "\n";
        }
    }
}
