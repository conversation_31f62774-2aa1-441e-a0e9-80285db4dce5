<?php
/**
 * Test script to apply a fix and check if it gets recorded in history
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🧪 Test Apply Fix and Check History</h2>\n";

// Step 1: Check current state
echo "<h3>Step 1: Current State</h3>\n";

$fix_history_before = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count before: " . count($fix_history_before) . "\n";

// Step 2: Apply a test fix
echo "<h3>Step 2: Applying Test Fix</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    $engine = new Redco_Diagnostic_AutoFix_Engine();
    
    // Create a test issue
    $test_issue = array(
        'id' => 'test_fix_' . time(),
        'title' => 'Test Fix for History Check',
        'description' => 'This is a test issue to check if fixes are recorded in history',
        'severity' => 'low',
        'category' => 'test',
        'auto_fixable' => true,
        'fix_action' => 'test_fix'
    );
    
    echo "Test issue created: " . $test_issue['id'] . "\n";
    echo "Applying fix using apply_fix() method...\n";
    
    try {
        // Use the new apply_fix method
        $fix_result = $engine->apply_fix($test_issue);
        
        echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>\n";
        echo "<h4>Fix Result:</h4>\n";
        echo "<pre>" . print_r($fix_result, true) . "</pre>\n";
        echo "</div>\n";
        
        if (isset($fix_result['success']) && $fix_result['success']) {
            echo "<p style='color: green;'>✅ Fix was applied successfully</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Fix failed</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception occurred: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Redco_Diagnostic_AutoFix_Engine class not found</p>\n";
}

// Step 3: Check if history was updated
echo "<h3>Step 3: Check History After Fix</h3>\n";

$fix_history_after = get_option('redco_diagnostic_fix_history', array());
echo "Fix history count after: " . count($fix_history_after) . "\n";

if (count($fix_history_after) > count($fix_history_before)) {
    echo "<p style='color: green;'>✅ Fix history was updated!</p>\n";
    
    // Show the latest session
    $latest_session = end($fix_history_after);
    echo "<h4>Latest Session:</h4>\n";
    echo "<div style='background: #e8f5e8; padding: 10px; margin: 10px 0;'>\n";
    echo "<ul>\n";
    echo "<li><strong>Timestamp:</strong> " . date('Y-m-d H:i:s', $latest_session['timestamp']) . "</li>\n";
    echo "<li><strong>Fixes Applied:</strong> " . $latest_session['fixes_applied'] . "</li>\n";
    echo "<li><strong>Fixes Failed:</strong> " . $latest_session['fixes_failed'] . "</li>\n";
    echo "<li><strong>Backup Created:</strong> " . ($latest_session['backup_created'] ? 'Yes' : 'No') . "</li>\n";
    echo "</ul>\n";
    
    if (isset($latest_session['details']) && is_array($latest_session['details'])) {
        echo "<h5>Fix Details:</h5>\n";
        foreach ($latest_session['details'] as $detail) {
            echo "<div style='margin-left: 20px; border-left: 2px solid #4CAF50; padding-left: 10px;'>\n";
            echo "<strong>Issue:</strong> " . ($detail['issue_title'] ?? 'Unknown') . "<br>\n";
            echo "<strong>Success:</strong> " . (($detail['success'] ?? false) ? 'Yes' : 'No') . "<br>\n";
            echo "<strong>Message:</strong> " . ($detail['message'] ?? 'No message') . "<br>\n";
            echo "</div>\n";
        }
    }
    echo "</div>\n";
} else {
    echo "<p style='color: red;'>❌ Fix history was NOT updated</p>\n";
    echo "<p>This indicates the fix was not properly recorded in the session history.</p>\n";
}

// Step 4: Test the AJAX endpoint
echo "<h3>Step 4: Test Recent Fixes AJAX Endpoint</h3>\n";

if (class_exists('Redco_Diagnostic_AutoFix')) {
    $diagnostic = new Redco_Diagnostic_AutoFix();
    
    // Simulate AJAX request
    $_POST['nonce'] = wp_create_nonce('redco_diagnostic_nonce');
    
    echo "Calling ajax_load_recent_fixes()...\n";
    
    // Capture output
    ob_start();
    try {
        $diagnostic->ajax_load_recent_fixes();
        $ajax_output = ob_get_clean();
        
        if (!empty($ajax_output)) {
            echo "<p style='color: green;'>✅ AJAX endpoint returned data</p>\n";
            echo "<div style='background: #f9f9f9; padding: 10px; margin: 10px 0;'>\n";
            echo "<h5>AJAX Response:</h5>\n";
            echo "<pre>" . htmlspecialchars($ajax_output) . "</pre>\n";
            echo "</div>\n";
        } else {
            echo "<p style='color: red;'>❌ AJAX endpoint returned no data</p>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ AJAX endpoint threw exception: " . $e->getMessage() . "</p>\n";
    }
} else {
    echo "<p style='color: red;'>❌ Redco_Diagnostic_AutoFix class not found</p>\n";
}

// Step 5: Summary
echo "<h3>Step 5: Summary</h3>\n";

$issues_found = array();

if (count($fix_history_after) <= count($fix_history_before)) {
    $issues_found[] = "Fix history not updated";
}

if (empty($ajax_output)) {
    $issues_found[] = "AJAX endpoint not working";
}

if (empty($issues_found)) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED!</p>\n";
    echo "<ul>\n";
    echo "<li>✅ Fix was applied successfully</li>\n";
    echo "<li>✅ Fix history was updated</li>\n";
    echo "<li>✅ AJAX endpoint is working</li>\n";
    echo "<li>✅ Recent fixes should be visible in the interface</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ ISSUES FOUND:</p>\n";
    echo "<ul>\n";
    foreach ($issues_found as $issue) {
        echo "<li style='color: red;'>❌ " . $issue . "</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h4>Debugging Steps:</h4>\n";
    echo "<ol>\n";
    echo "<li>Check PHP error logs for any errors during fix application</li>\n";
    echo "<li>Verify database permissions for updating options</li>\n";
    echo "<li>Check if any other plugins are interfering with the option updates</li>\n";
    echo "<li>Enable WP_DEBUG to see detailed logging</li>\n";
    echo "</ol>\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> Check the PHP error log for detailed debugging information from the engine.</p>\n";
?>
