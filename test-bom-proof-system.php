<?php
/**
 * Comprehensive test for BOM-proof .htaccess system
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('This script must be run from WordPress admin.');
}

echo "<h2>🛡️ BOM-Proof .htaccess System Test</h2>\n";

// Step 1: Test BOM detection and removal
echo "<h3>Step 1: BOM Detection and Removal Test</h3>\n";

if (!class_exists('Redco_Diagnostic_AutoFix_Engine')) {
    echo "<p style='color: red;'>❌ Redco_Diagnostic_AutoFix_Engine class not found</p>\n";
    return;
}

$engine = new Redco_Diagnostic_AutoFix_Engine();

// Create test content with different types of BOM
$test_contents = array(
    'UTF-8 BOM' => "\xEF\xBB\xBF# Test .htaccess content\nRewriteEngine On\n",
    'UTF-16 BE BOM' => "\xFE\xFF# Test .htaccess content\nRewriteEngine On\n",
    'UTF-16 LE BOM' => "\xFF\xFE# Test .htaccess content\nRewriteEngine On\n",
    'UTF-32 BE BOM' => "\x00\x00\xFE\xFF# Test .htaccess content\nRewriteEngine On\n",
    'UTF-32 LE BOM' => "\xFF\xFE\x00\x00# Test .htaccess content\nRewriteEngine On\n",
    'No BOM' => "# Test .htaccess content\nRewriteEngine On\n"
);

$bom_tests_passed = 0;
$total_bom_tests = count($test_contents);

foreach ($test_contents as $bom_type => $content) {
    echo "<h4>Testing: {$bom_type}</h4>\n";
    
    // Use reflection to access private method for testing
    $reflection = new ReflectionClass($engine);
    $detect_method = $reflection->getMethod('detect_and_remove_bom');
    $detect_method->setAccessible(true);
    
    $result = $detect_method->invoke($engine, $content);
    
    $expected_bom = ($bom_type !== 'No BOM');
    $bom_detected = $result['bom_detected'];
    
    if ($expected_bom === $bom_detected) {
        echo "✅ BOM detection correct: " . ($bom_detected ? "Detected {$result['bom_type']}" : "No BOM detected") . "\n";
        
        if ($bom_detected) {
            $clean_content = $result['content'];
            $expected_clean = "# Test .htaccess content\nRewriteEngine On\n";
            
            if ($clean_content === $expected_clean) {
                echo "✅ BOM removal successful\n";
                $bom_tests_passed++;
            } else {
                echo "❌ BOM removal failed - content mismatch\n";
                echo "Expected: " . bin2hex($expected_clean) . "\n";
                echo "Got: " . bin2hex($clean_content) . "\n";
            }
        } else {
            $bom_tests_passed++;
        }
    } else {
        echo "❌ BOM detection failed - Expected: " . ($expected_bom ? 'true' : 'false') . ", Got: " . ($bom_detected ? 'true' : 'false') . "\n";
    }
    
    echo "<br>\n";
}

echo "BOM Tests: {$bom_tests_passed}/{$total_bom_tests} passed\n";

// Step 2: Test .htaccess syntax validation
echo "<h3>Step 2: .htaccess Syntax Validation Test</h3>\n";

$syntax_tests = array(
    'Valid basic' => "RewriteEngine On\nRewriteRule ^test$ /index.php [L]",
    'Valid with modules' => "<IfModule mod_rewrite.c>\nRewriteEngine On\n</IfModule>",
    'Valid headers' => "<IfModule mod_headers.c>\nHeader set X-Test \"value\"\n</IfModule>",
    'Invalid unclosed module' => "<IfModule mod_rewrite.c>\nRewriteEngine On",
    'Invalid directive' => "InvalidDirective123 test",
    'Empty content' => ""
);

$syntax_method = $reflection->getMethod('validate_htaccess_basic_syntax');
$syntax_method->setAccessible(true);

$syntax_tests_passed = 0;
$total_syntax_tests = count($syntax_tests);

foreach ($syntax_tests as $test_name => $content) {
    $is_valid = $syntax_method->invoke($engine, $content);
    $expected_valid = !in_array($test_name, array('Invalid unclosed module', 'Invalid directive'));
    
    if ($is_valid === $expected_valid) {
        echo "✅ {$test_name}: " . ($is_valid ? 'Valid' : 'Invalid') . " (as expected)\n";
        $syntax_tests_passed++;
    } else {
        echo "❌ {$test_name}: Expected " . ($expected_valid ? 'valid' : 'invalid') . ", got " . ($is_valid ? 'valid' : 'invalid') . "\n";
    }
}

echo "Syntax Tests: {$syntax_tests_passed}/{$total_syntax_tests} passed\n";

// Step 3: Test emergency BOM cleaning
echo "<h3>Step 3: Emergency BOM Cleaning Test</h3>\n";

$htaccess_file = ABSPATH . '.htaccess';
$backup_original = null;

// Create backup of original .htaccess if it exists
if (file_exists($htaccess_file)) {
    $backup_original = $htaccess_file . '.test-backup-' . time();
    copy($htaccess_file, $backup_original);
    echo "✅ Created backup of original .htaccess\n";
}

// Create test .htaccess with BOM
$test_content_with_bom = "\xEF\xBB\xBF# Test .htaccess with UTF-8 BOM\nRewriteEngine On\n# Test content\n";
file_put_contents($htaccess_file, $test_content_with_bom);
echo "✅ Created test .htaccess with UTF-8 BOM\n";

// Test emergency BOM cleaning
$clean_result = $engine->clean_htaccess_bom();

if ($clean_result['success']) {
    echo "✅ Emergency BOM cleaning successful\n";
    echo "BOM Type: " . $clean_result['bom_type'] . "\n";
    echo "Original Size: " . $clean_result['original_size'] . " bytes\n";
    echo "Cleaned Size: " . $clean_result['cleaned_size'] . " bytes\n";
    echo "Bytes Saved: " . $clean_result['bytes_saved'] . " bytes\n";
    
    // Verify the file is now BOM-free
    $verification_content = file_get_contents($htaccess_file);
    $verify_bom = $detect_method->invoke($engine, $verification_content);
    
    if (!$verify_bom['bom_detected']) {
        echo "✅ Verification: File is now BOM-free\n";
    } else {
        echo "❌ Verification failed: BOM still present (" . $verify_bom['bom_type'] . ")\n";
    }
} else {
    echo "❌ Emergency BOM cleaning failed: " . $clean_result['message'] . "\n";
}

// Step 4: Test BOM-safe fix methods
echo "<h3>Step 4: BOM-Safe Fix Methods Test</h3>\n";

// Test security header fix with BOM protection
$test_issue = array(
    'id' => 'missing_security_header_X-Content-Type-Options',
    'title' => 'Missing X-Content-Type-Options header',
    'fix_action' => 'add_security_header'
);

$result = array('success' => false, 'changes_made' => array());

// Use reflection to access private method
$fix_method = $reflection->getMethod('fix_add_security_header');
$fix_method->setAccessible(true);

$fix_result = $fix_method->invoke($engine, $test_issue, $result);

if ($fix_result['success']) {
    echo "✅ Security header fix applied successfully\n";
    echo "Changes made: " . implode(', ', $fix_result['changes_made']) . "\n";
    
    // Check if backup was created
    if (isset($fix_result['backup_file'])) {
        echo "✅ Backup created: " . basename($fix_result['backup_file']) . "\n";
    }
} else {
    echo "❌ Security header fix failed: " . $fix_result['message'] . "\n";
}

// Step 5: Restore original .htaccess
echo "<h3>Step 5: Cleanup</h3>\n";

if ($backup_original && file_exists($backup_original)) {
    copy($backup_original, $htaccess_file);
    unlink($backup_original);
    echo "✅ Restored original .htaccess file\n";
} else {
    // If no original backup, create a minimal .htaccess
    file_put_contents($htaccess_file, "# WordPress .htaccess\nRewriteEngine On\n");
    echo "✅ Created minimal .htaccess file\n";
}

// Clean up any test backup files
$backup_files = glob($htaccess_file . '.redco-backup-*');
foreach ($backup_files as $backup_file) {
    if (file_exists($backup_file)) {
        unlink($backup_file);
        echo "✅ Cleaned up backup: " . basename($backup_file) . "\n";
    }
}

// Step 6: Summary
echo "<h3>Step 6: Test Summary</h3>\n";

$total_tests = $total_bom_tests + $total_syntax_tests + 2; // +2 for emergency clean and fix method
$passed_tests = $bom_tests_passed + $syntax_tests_passed;

if ($clean_result['success']) $passed_tests++;
if ($fix_result['success']) $passed_tests++;

if ($passed_tests === $total_tests) {
    echo "<p style='color: green; font-weight: bold;'>🎉 ALL TESTS PASSED ({$passed_tests}/{$total_tests})</p>\n";
    echo "<ul>\n";
    echo "<li>✅ BOM detection and removal working correctly</li>\n";
    echo "<li>✅ .htaccess syntax validation working</li>\n";
    echo "<li>✅ Emergency BOM cleaning functional</li>\n";
    echo "<li>✅ BOM-safe fix methods operational</li>\n";
    echo "<li>✅ File backup and restoration working</li>\n";
    echo "</ul>\n";
    
    echo "<h4>🛡️ BOM Protection Features:</h4>\n";
    echo "<ul>\n";
    echo "<li>✅ Comprehensive BOM detection (UTF-8, UTF-16, UTF-32)</li>\n";
    echo "<li>✅ Automatic BOM removal during read operations</li>\n";
    echo "<li>✅ BOM-safe file writing with LOCK_EX</li>\n";
    echo "<li>✅ Pre-write syntax validation</li>\n";
    echo "<li>✅ Post-write verification</li>\n";
    echo "<li>✅ Automatic backup creation</li>\n";
    echo "<li>✅ Rollback on failure</li>\n";
    echo "</ul>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ SOME TESTS FAILED ({$passed_tests}/{$total_tests})</p>\n";
    echo "<p>Please check the individual test results above for details.</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Note:</strong> The BOM-proof system is now active and will automatically prevent BOM issues in all .htaccess operations.</p>\n";
?>
